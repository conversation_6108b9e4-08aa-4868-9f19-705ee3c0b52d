/* 轮播图容器 */
.carousel-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: white;
}

/* Swiper 容器 */
.carousel-swiper {
  width: 100%;
  aspect-ratio: 3/1; /* 确保3:1比例 */
  position: relative;
}

/* 轮播图片区域 */
.carousel-slide {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.slide-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 轮播内容覆盖层 */
.slide-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 600px;
}

.slide-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.slide-subtitle {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.95;
}

.slide-description {
  font-size: 1.125rem;
  font-weight: 400;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.9;
  line-height: 1.4;
}

/* 了解更多按钮 */
.carousel-actions {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  z-index: 3;
}

.learn-more-btn {
  background: linear-gradient(135deg, #4f9cf9 0%, #1e40af 100%);
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(79, 156, 249, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.learn-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(79, 156, 249, 0.4);
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.learn-more-btn:active {
  transform: translateY(0);
}

/* Swiper 导航按钮自定义 */
.carousel-swiper .swiper-button-next,
.carousel-swiper .swiper-button-prev {
  color: white;
  background: rgba(0, 0, 0, 0.3);
  width: 44px;
  height: 44px;
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.carousel-swiper .swiper-button-next:after,
.carousel-swiper .swiper-button-prev:after {
  font-size: 18px;
  font-weight: 600;
}

.carousel-swiper .swiper-button-next:hover,
.carousel-swiper .swiper-button-prev:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(1.1);
}

/* Swiper 分页器自定义 */
.carousel-swiper .swiper-pagination {
  bottom: 1rem;
  left: 2rem;
  width: auto;
  text-align: left;
}

.carousel-swiper .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;
  margin: 0 6px;
  transition: all 0.3s ease;
}

.carousel-swiper .swiper-pagination-bullet-active {
  background: white;
  transform: scale(1.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .carousel-container {
    border-radius: 12px;
  }
  
  .slide-content {
    padding: 1.5rem;
    max-width: 90%;
  }
  
  .slide-title {
    font-size: 2rem;
  }
  
  .slide-subtitle {
    font-size: 1.25rem;
  }
  
  .slide-description {
    font-size: 1rem;
  }
  
  .carousel-actions {
    bottom: 1.5rem;
    right: 1.5rem;
  }
  
  .learn-more-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
  
  .carousel-swiper .swiper-button-next,
  .carousel-swiper .swiper-button-prev {
    width: 36px;
    height: 36px;
  }
  
  .carousel-swiper .swiper-button-next:after,
  .carousel-swiper .swiper-button-prev:after {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .slide-content {
    padding: 1rem;
  }
  
  .slide-title {
    font-size: 1.5rem;
  }
  
  .slide-subtitle {
    font-size: 1rem;
  }
  
  .slide-description {
    font-size: 0.9rem;
  }
  
  .carousel-actions {
    bottom: 1rem;
    right: 1rem;
  }
  
  .learn-more-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;
  }
}