/* 今日工作台样式 */
.daily-workspace {
  margin: 2rem 0;
  padding: 0 1rem;
}

.daily-workspace-header {
  text-align: center;
  margin-bottom: 2rem;
}

.daily-workspace-header h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.daily-workspace-header p {
  color: #6b7280;
  font-size: 0.95rem;
  margin: 0;
}

.daily-workspace-cards {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* 卡片通用样式 */
.workspace-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.workspace-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.workspace-card-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.workspace-card-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.workspace-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  flex-grow: 1;
}

.workspace-card-content {
  flex-grow: 1;
}

/* 今日安排卡片特定样式 */
.schedule-card {
  grid-column: span 1;
}

.schedule-card .workspace-card-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.schedule-countdown {
  background: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.countdown-time {
  font-size: 2rem;
  font-weight: 700;
  color: #3b82f6;
  font-family: 'Courier New', monospace;
  margin-bottom: 0.25rem;
}

.countdown-label {
  color: #6b7280;
  font-size: 0.875rem;
}

.schedule-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.schedule-stat {
  flex: 1;
  text-align: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
}

.schedule-stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.schedule-stat-label {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.next-visitor {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 1rem;
}

.next-visitor-name {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 0.25rem;
}

.next-visitor-info {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.no-appointments {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.no-appointments-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.create-appointment-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 1rem;
}

.create-appointment-btn:hover {
  background: #2563eb;
}

/* 预约即将开始的警告样式 */
.schedule-card.upcoming-warning {
  border-color: #fbbf24;
  box-shadow: 0 0 0 1px #fbbf24, 0 4px 6px rgba(0, 0, 0, 0.1);
}

.schedule-card.upcoming-warning .countdown-time {
  color: #f59e0b;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 每日心语卡片特定样式 */
.message-card .workspace-card-icon {
  background: linear-gradient(135deg, #ec4899, #be185d);
  color: white;
}

.daily-message {
  margin-bottom: 1.5rem;
}

.message-quote {
  background: #fdf2f8;
  border-left: 4px solid #ec4899;
  padding: 1rem;
  border-radius: 0 8px 8px 0;
  margin-bottom: 1rem;
}

.message-content {
  font-style: italic;
  color: #1f2937;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.message-author {
  text-align: right;
  font-size: 0.875rem;
  color: #6b7280;
}

.message-selfcare {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  padding: 1rem;
  border-radius: 8px;
  color: #0c4a6e;
  line-height: 1.5;
}

.refresh-message-btn {
  background: #ec4899;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.refresh-message-btn:hover {
  background: #db2777;
}

.refresh-message-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 快速操作卡片特定样式 */
.actions-card .workspace-card-icon {
  background: linear-gradient(135deg, #10b981, #047857);
  color: white;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.quick-action-btn {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.quick-action-btn:hover {
  border-color: #10b981;
  background: #f0fdf4;
}

.quick-action-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #10b981;
  flex-shrink: 0;
}

.quick-action-text {
  flex-grow: 1;
}

.quick-action-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.quick-action-desc {
  font-size: 0.75rem;
  color: #6b7280;
}

.quick-note-preview {
  background: #fffbeb;
  border: 1px solid #fed7aa;
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 1rem;
}

.quick-note-time {
  font-size: 0.75rem;
  color: #92400e;
  margin-bottom: 0.25rem;
}

.quick-note-content {
  font-size: 0.875rem;
  color: #1f2937;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .daily-workspace-cards {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .schedule-card {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .daily-workspace {
    margin: 1rem 0;
    padding: 0 0.5rem;
  }
  
  .daily-workspace-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .schedule-card {
    grid-column: span 1;
  }
  
  .workspace-card {
    padding: 1rem;
  }
  
  .schedule-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .countdown-time {
    font-size: 1.5rem;
  }
}

/* 加载状态 */
.workspace-card.loading {
  opacity: 0.7;
}

.loading-skeleton {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  height: 1rem;
  margin-bottom: 0.5rem;
}

.loading-skeleton.wide {
  width: 100%;
}

.loading-skeleton.medium {
  width: 70%;
}

.loading-skeleton.narrow {
  width: 50%;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
