import React from 'react';
import { TodayScheduleCard } from './TodayScheduleCard';
import { DailyMessageCard } from './DailyMessageCard';
import { QuickActionsCard } from './QuickActionsCard';
import './DailyWorkspace.css';

interface DailyWorkspaceSectionProps {
  onNavigate?: (page: 'dashboard' | 'visitors' | 'cases' | 'schedule' | 'group-sessions' | 'sandtools' | 'statistics' | 'settings' | 'help' | 'dev-browser') => void;
}

export const DailyWorkspaceSection: React.FC<DailyWorkspaceSectionProps> = ({ onNavigate }) => {
  return (
    <div className="daily-workspace">
      <div className="daily-workspace-header">
        <h2>📋 今日工作台</h2>
        <p>快速了解今日安排，获取每日心语，执行常用操作</p>
      </div>
      
      <div className="daily-workspace-cards">
        <TodayScheduleCard onNavigate={onNavigate} />
        <DailyMessageCard />
        <QuickActionsCard onNavigate={onNavigate} />
      </div>
    </div>
  );
};
